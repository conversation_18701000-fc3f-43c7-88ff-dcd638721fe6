%matplotlib inline
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import re

marketing_data = pd.read_csv('marketing_data.csv')
pd.set_option('display.max_columns', None)

marketing_data.head()

import warnings
warnings.filterwarnings('ignore')


marketing_data.columns

marketing_data.info()

marketing_data.isnull().sum()

print(marketing_data.describe())

# Cleaning the column

#taking dollar sign out of values and converting to float
marketing_data['Income'] = marketing_data['Income'].replace('[$,]', '', regex=True).astype(float)

# Summary stats
print(marketing_data['Income'].describe())

# putting all columns into snake case format
marketing_data.columns = [re.sub(r'(?<!^)(?=[A-Z])', '_', x).lower() for x in marketing_data.columns]

print(marketing_data.columns)

plt.figure()
sns.boxplot(marketing_data["income"])
plt.show()

# remove incomes below 300000
marketing_data = marketing_data[marketing_data['income']<300000]

plt.figure()
sns.boxplot(marketing_data["income"])
plt.show()


marketing_data['income'].describe()

marketing_data['income'] = marketing_data['income'].fillna(marketing_data['income'].mean())

marketing_data['age'] = 2025 - marketing_data['year_birth']

plt.figure()
sns.boxplot(marketing_data['age'])
plt.show()

marketing_data['age'].describe()

marketing_data = marketing_data[marketing_data['age'] < 100]

plt.figure()
sns.boxplot(marketing_data["age"])
plt.show()

marketing_data["age"].describe()


from seaborn import color_palette, palettes


plt.figure()
sns.boxplot(x = marketing_data["education"], y = marketing_data["income"], order=["Basic", "Graduation", "2n Cycle", "Master", "PhD"], palette="Set1")

plt.show()


marketing_data = marketing_data[marketing_data['income'] < 200000]

plt.figure()
sns.boxplot(marketing_data["income"])
plt.show()

marketing_data["income"].describe()

fig = plt.figure(figsize=(10, 20))
ax = fig.gca()
marketing_data.hist(ax=ax)
plt.show()


marketing_data.corr(numeric_only=True)

plt.figure(figsize=(10,10))
sns.heatmap(data=marketing_data.corr(numeric_only=True),annot=False)
plt.tight_layout()
plt.show()

num_purchases_cols = ["num_deals_purchases", "num_web_purchases", "num_catalog_purchases", "num_store_purchases"]
plot = 0
fig = plt.figure(figsize=(8,8))
for i in range(len(num_purchases_cols)):
    plot += 1
    plt.subplot(2,2,plot)
    sns.scatterplot(x=marketing_data["income"], y=marketing_data[num_purchases_cols[i]], hue=marketing_data["education"], palette="viridis")
    plt.title("Purchase Behaviour by Income")
plt.show()

num_purchases_cols = ["num_deals_purchases", "num_web_purchases", "num_catalog_purchases", "num_store_purchases"]

fig = plt.figure(figsize=(10, 9))
plot = 0
for i in range(len(num_purchases_cols)):
    plot += 1
    plt.subplot(2,2,plot)
    sns.regplot(x=marketing_data["income"], y=marketing_data[num_purchases_cols[i]], scatter_kws={'color':'black'}, line_kws={'color':'red'})
    plt.title("Purchase Behaviour by Income")
plt.tight_layout()
plt.show()

# adding new total_purchases column to the dataframe
marketing_data['total_purchases'] = marketing_data['num_deals_purchases'] + marketing_data['num_web_purchases'] + marketing_data['num_store_purchases']

plt.figure()
sns.scatterplot(x=marketing_data['income'], y=marketing_data['total_purchases'], hue=marketing_data['education'], palette='viridis')
plt.title("Purchase Behaviour by Income")
plt.show()

plt.figure()
sns.regplot(x=marketing_data["income"], y=marketing_data["total_purchases"], scatter_kws={'color':'black'}, line_kws={'color':'red'})
plt.show()

amounts_list = ['mnt_wines', 'mnt_fruits', 'mnt_meat_products', 'mnt_fish_products', 'mnt_sweet_products', 'mnt_gold_products']
plot = 0
fig = plt.figure(figsize=(8,6))
for i in range(len(amounts_list)):
    plot += 1
    ax = plt.subplot(3, 2, plot)
    sns.scatterplot(y=marketing_data[amounts_list[i]], x=marketing_data['income'])
    plt.title('Purchase Behavior by Income')
plt.tight_layout()
plt.show()

amounts_list = ["mnt_wines", "mnt_fruits", "mnt_meat_products", "mnt_fish_products", "mnt_sweet_products", "mnt_gold_products"]
axes = [None for column in amounts_list]
plot = 0
fig = plt.figure(figsize=(8,10))
for i in range(len(amounts_list)):
    plot += 1
    ax = plt.subplot(3, 2, plot)
    sns.scatterplot(y=np.log(marketing_data[amounts_list[i]]), x=np.log(marketing_data["income"]), 
                    hue=marketing_data["education"], palette="viridis")
    ax.set_xlabel("Log Scaled income")
    plt.title("Purchase Behavior by income")
plt.tight_layout()
plt.show()

variable_names = {}
amounts_list = ["mnt_wines", "mnt_fruits", "mnt_meat_products", "mnt_fish_products", "mnt_sweet_products", "mnt_gold_products"]

for i in range(len(amounts_list)):
    variable_names["g{0}".format(i)] = sns.FacetGrid(marketing_data, col='education', col_wrap=3)
    variable_names["g{0}".format(i)].map(sns.scatterplot, "income",amounts_list[i])
plt.show()

plt.figure()
sns.scatterplot(x=marketing_data['age'], y=marketing_data['total_purchases'])
plt.title('Purchase Behaviour by Age')
plt.show()

# amounts columns list
amounts_list = ["mnt_wines", "mnt_fruits", "mnt_meat_products", "mnt_fish_products", "mnt_sweet_products", "mnt_gold_products"]
plot = 0
fig = plt.figure(figsize=(9,10))
# looping through the length of the amounts columns list
for i in range(len(amounts_list)):
    plot += 1
    # setting up the subplots
    ax = plt.subplot(3, 2, plot)
    # plotting amounts purchased vs. income
    sns.scatterplot(y= marketing_data[amounts_list[i]], x=marketing_data["age"])
    plt.title("Purchasing Behavior by age")
plt.tight_layout()
plt.show()

# function that breaks up each age into a group
def age_group(age):
    if 22 <= age <= 35:
        return '22 to 35'
    elif 35 < age <=50:
        return '36 to 50'
    elif 50 < age <=65:
        return '51 to 65'
    else:
        return '65 and over'
    
# applying the function to the dataframe
marketing_data['age_group'] = marketing_data['age'].apply(age_group)

plt.figure()
sns.countplot(marketing_data['age_group'], order=['22 to 35', '36 to 50', '51 to 65', '65 and over'], palette='Set2')
plt.show()

marketing_data['age_group'].value_counts()

age_plus_amounts = marketing_data.loc[:, ('age', 'mnt_wines', 'mnt_fruits', 'mnt_meat_products', 'mnt_fish_products', 
'mnt_sweet_products', 'mnt_gold_products')]
bins = [22, 35, 50, 70, 90]
labels = ['22 to 35', '36 to 50', '51 to 65', '65 and over']

age_plus_amounts['age_group'] = pd.cut(age_plus_amounts['age'], bins=bins, labels=labels)
age_plus_amounts = age_plus_amounts.groupby('age_group').sum().reset_index()

print(age_plus_amounts)

fig, ax = plt.subplots(figsize=(9, 6))

col_products = [
'mnt_wines', 
'mnt_fruits', 
'mnt_meat_products', 
'mnt_fish_products', 
'mnt_sweet_products', 
'mnt_gold_products'
]

# bar graph of amount of items bought in the store grouped by age group
age_plus_amounts.plot(x='age_group', y=col_products, kind='bar', ax=ax,  width=0.8, edgecolor='#131313')
plt.title("How Much Customers Buy by age Group")
plt.ylabel("Total Products Purchased")
plt.tight_layout()
plt.show()

# num purchases columns list
num_purchases_cols = ["num_deals_purchases", "num_web_purchases", "num_catalog_purchases", "num_store_purchases"]
plot = 0
fig = plt.figure(figsize=(8,8))
# looping through the length of the list
for i in range(len(num_purchases_cols)):
    plot += 1
    # setting up the subplots
    ax = plt.subplot(2, 2, plot)
    # scatterplot of of num purchases vs. age
    sns.boxplot(x=marketing_data["age_group"], y= marketing_data[num_purchases_cols[i]], 
                order=["22 to 35", "36 to 50", "51 to 65", "65 and over"], palette='Set1')
    plt.title("Purchase Behavior by age")

plt.tight_layout()
plt.show()